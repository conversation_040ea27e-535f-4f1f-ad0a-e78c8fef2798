<?php if (isset($component)) { $__componentOriginalecda78b9fe8916cbd83b85e55a8b7a1c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalecda78b9fe8916cbd83b85e55a8b7a1c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'a74ad8dfacd4f985eb3977517615ce25::alert','data' => ['type' => 'info','important' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::alert'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'info','important' => true]); ?>
    <?php if($manageLicense = auth()->guard()->user()->hasPermission('core.manage.license')): ?>
        <div>Your license is Activated.</div>
    <?php else: ?>
        <div>Your license is Activated.</div>
    <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalecda78b9fe8916cbd83b85e55a8b7a1c)): ?>
<?php $attributes = $__attributesOriginalecda78b9fe8916cbd83b85e55a8b7a1c; ?>
<?php unset($__attributesOriginalecda78b9fe8916cbd83b85e55a8b7a1c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalecda78b9fe8916cbd83b85e55a8b7a1c)): ?>
<?php $component = $__componentOriginalecda78b9fe8916cbd83b85e55a8b7a1c; ?>
<?php unset($__componentOriginalecda78b9fe8916cbd83b85e55a8b7a1c); ?>
<?php endif; ?>



<!-- License Information Section -->
<div class="<?php echo e(AdminAppearance::getContainerWidth()); ?>">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <strong>License Information – Delwest Pvt Ltd</strong>
            <br>

        </div>

        <?php if($manageLicense): ?>
            <a
                class="btn-close"
                data-bs-toggle="modal"
                data-bs-target="#quick-activation-license-modal"
                aria-label="close"
            ></a>
        <?php endif; ?>
    </div>
</div>

<?php /**PATH G:\DL\app\app\platform/core/base/resources/views/components/license/form.blade.php ENDPATH**/ ?>