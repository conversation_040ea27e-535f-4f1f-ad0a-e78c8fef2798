<?php if (! $__env->hasRenderedOnce('681f501a-e4c3-4ad6-abea-a5b0cc3cdf59')): $__env->markAsRenderedOnce('681f501a-e4c3-4ad6-abea-a5b0cc3cdf59'); ?>
    <div
        class="offcanvas offcanvas-end"
        tabindex="-1"
        id="notification-sidebar"
        aria-labelledby="notification-sidebar-label"
        data-url="<?php echo e(route('notifications.index')); ?>"
        data-count-url="<?php echo e(route('notifications.count-unread')); ?>"
    >
        <button
            type="button"
            class="btn-close text-reset"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
        ></button>

        <div class="notification-content"></div>
    </div>

    <script src="<?php echo e(asset('vendor/core/core/base/js/notification.js')); ?>"></script>
<?php endif; ?>
<?php /**PATH G:\DL\app\app\platform/core/base/resources/views/notification/notification.blade.php ENDPATH**/ ?>