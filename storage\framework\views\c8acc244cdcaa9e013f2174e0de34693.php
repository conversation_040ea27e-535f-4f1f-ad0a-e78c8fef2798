<?php
    $margin = $margin ?? false;
?>

<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['single-services shadow-block mb-30', 'ser-m' => !$margin]); ?>">
    <div class="services-thumb hover-zoomin wow fadeInUp animated">
        <?php if($images = $room->images): ?>
            <a href="<?php echo e($room->url); ?>?start_date=<?php echo e(BaseHelper::stringify(request()->query('start_date', $startDate))); ?>&end_date=<?php echo e(BaseHelper::stringify(request()->query('end_date', $endDate))); ?>&adults=<?php echo e(BaseHelper::stringify(request()->query('adults', HotelHelper::getMinimumNumberOfGuests()))); ?>">
                <img src="<?php echo e(RvMedia::getImageUrl(Arr::first($images), 'medium')); ?>" alt="<?php echo e($room->name); ?>">
            </a>
        <?php endif; ?>
    </div>
    <div class="services-content">
        <?php if(HotelHelper::isBookingEnabled()): ?>
            <div class="day-book">
                <ul>
                    <li>
                        <form action="<?php echo e(route('public.booking')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="room_id" value="<?php echo e($room->id); ?>">
                            <input type="hidden" name="start_date" value="<?php echo e($startDate->format(HotelHelper::getDateFormat())); ?>">
                            <input type="hidden" name="end_date" value="<?php echo e($endDate->format(HotelHelper::getDateFormat())); ?>">
                            <input type="hidden" name="adults" value="<?php echo e($adults); ?>">
                            <input name="children" type="hidden" value="<?php echo e(BaseHelper::stringify(request()->integer('children')) ?: 0); ?>">
                            <input name="rooms" type="hidden" value="<?php echo e(BaseHelper::stringify(request()->integer('rooms', 1))); ?>">
                            <button class="book-button-custom" type="submit" data-animation="fadeInRight" data-delay=".8s">
                                <?php echo e(__('BOOK NOW FOR :price', ['price' => format_price($room->total_price)])); ?>

                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        <?php endif; ?>
        <h4><a href="<?php echo e($room->url); ?>"><?php echo e($room->name); ?></a></h4>
        <?php if($description = $room->description): ?>
            <p class="room-item-custom-truncate" title="<?php echo e($description); ?>"><?php echo BaseHelper::clean($description); ?></p>
        <?php endif; ?>

        <?php if($room->amenities->isNotEmpty()): ?>
            <div class="icon">
                <ul class="d-flex justify-content-evenly">
                    <?php $__currentLoopData = $room->amenities->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $amenity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($image = $amenity->getMetaData('icon_image', true) ): ?>
                            <li>
                                <img src="<?php echo e(RvMedia::getImageUrl($image)); ?>" alt="<?php echo e($amenity->name); ?>">
                            </li>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH G:\DL\app\app\platform\themes/riorelax/partials/rooms/item.blade.php ENDPATH**/ ?>